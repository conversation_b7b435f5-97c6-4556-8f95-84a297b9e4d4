# 重要配置项（请根据您的实际情况修改）

# 1. API密钥 - 替换为您自己的密钥
API_KEY = "sk-4xpeNs1TG1HAuhsJ1f0c9f7f0f2b4598Ab521b7c21A55632"

# 2. 默认模型选择
LLM_MODEL = "gpt-3.5-turbo"  # 使用标准OpenAI模型

# 3. 可用模型列表（根据您的实际模型调整）
AVAIL_LLM_MODELS = [
    "gpt-3.5-turbo",
    "gpt-3.5-turbo-16k",
    "gpt-4",
    "gpt-4-turbo",
    "gpt-4o",
    "gpt-4o-mini"
]

# 4. API重定向（如果使用one-api）
API_URL_REDIRECT = {
    "https://api.openai.com/v1/chat/completions": "http://localhost:30001/v1/chat/completions"
}

# 5. 并发线程数（根据您的硬件调整）
DEFAULT_WORKER_NUM = 10  # 本地建议5-20

# 6. 网页端口
WEB_PORT = 55996

# 7. 是否自动打开浏览器
AUTO_OPEN_BROWSER = True

# 8. 超时设置
TIMEOUT_SECONDS = 300

# 9. 重试次数
MAX_RETRY = 5